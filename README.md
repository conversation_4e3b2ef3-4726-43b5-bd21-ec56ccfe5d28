# SASK (Spring AI Semantic Kernel Adapter)

> Spring AI for Semantic Kernel is an adapter that bridges old Semantic Kernel Java concepts to Spring AI with modern features

## 项目结构

这是一个多模块项目，包含以下模块：

### 核心模块

- **sask-core** - 核心适配器，包含：
  - `kernel` - SASK 核心执行引擎
  - `template` - 基于 Handlebars 的提示词模板引擎
  - `chat` - Spring AI ChatClient 的封装
  - `tool` - 工具/插件注册和管理
  - `plan` - AI 驱动的任务规划器

- **sask-sql** - SQL 处理模块，包含：
  - `parser` - 基于 JSQLParser 的 SQL 解析和验证
  - `generator` - 使用 AI 从自然语言生成 SQL
  - `analyzer` - SQL 分析工具

- **sask-data** - 数据处理模块，包含：
  - `loader` - 多源数据加载器（CSV、JSON、API）
  - `processor` - 基于 Tablesaw 的数据处理
  - `transformer` - 数据转换和清洗工具

- **sask-connectors** - 连接器模块，包含：
  - `milvus` - Milvus 向量数据库连接器
  - `vector` - Spring AI VectorStore 集成
  - `web` - HTTP API 连接器

- **sask-example** - 示例应用，展示如何使用所有模块

## 快速开始

### 1. 环境要求

- Java 17+
- Gradle 8.14+
- OpenAI API Key（用于 AI 功能）
- Milvus 数据库（可选，用于向量存储）

### 2. 配置

在 `sask-example/src/main/resources/application.yml` 中配置：

```yaml
spring:
  ai:
    openai:
      api-key: your-openai-api-key

milvus:
  host: localhost
  port: 19530
```

### 3. 运行示例

```bash
./gradlew :sask-example:bootRun
```

## 模块依赖关系

```
sask-example
├── sask-core (核心功能)
├── sask-sql (依赖 sask-core)
├── sask-data (依赖 sask-core)
└── sask-connectors (依赖 sask-core)
```

## 主要特性

- ✅ **多模块架构** - 清晰的模块分离和依赖管理
- ✅ **Spring AI 集成** - 现代化的 AI 模型接口
- ✅ **向量数据库支持** - Milvus 集成用于语义搜索
- ✅ **SQL 智能处理** - 自然语言到 SQL 的转换
- ✅ **数据处理管道** - 完整的 ETL 功能
- ✅ **模板引擎** - 动态提示词生成
- ✅ **工具系统** - 可扩展的插件架构
- ✅ **任务规划** - AI 驱动的多步骤任务执行

## 从旧版 Semantic Kernel 迁移

SASK 提供了与旧版 Semantic Kernel Java 兼容的 API，主要映射关系：

| Semantic Kernel | SASK | 说明 |
|----------------|------|------|
| Kernel | SaskKernel | 核心执行引擎 |
| Plugin | Tool/ToolRegistry | 插件系统 |
| Function | Function/ToolRegistry | 函数调用 |
| Memory | VectorStoreService | 记忆存储 |
| Planner | Planner | 任务规划 |
| Connector | WebConnector/MilvusConnector | 外部服务连接 |

## 开发指南

### 添加新的工具

```kotlin
@Component
class MyCustomTool : Tool {
    override val name = "my-tool"
    override val description = "My custom tool"

    override fun execute(input: Any): Any {
        // 实现工具逻辑
        return "result"
    }
}
```

### 使用 SQL 生成器

```kotlin
@Service
class MyService(private val sqlGenerator: SqlGenerator) {

    fun generateQuery(request: String, schema: DatabaseSchema): String {
        return sqlGenerator.generateSql(request, schema)
    }
}
```

### 数据处理示例

```kotlin
@Service
class DataService(private val dataProcessor: DataProcessor) {

    fun processData(jsonData: String): Table {
        val table = dataProcessor.jsonToTable(jsonData)
        return dataProcessor.filter(table, "status") { it == "active" }
    }
}
```

## 许可证

MIT License
