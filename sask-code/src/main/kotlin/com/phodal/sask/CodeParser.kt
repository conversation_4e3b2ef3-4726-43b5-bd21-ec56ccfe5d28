package com.phodal.sask

import io.github.treesitter.jtreesitter.InputEncoding
import io.github.treesitter.jtreesitter.Language
import io.github.treesitter.jtreesitter.Node
import io.github.treesitter.jtreesitter.Parser


class CodeParser {
    fun parse() {
        val language: Language = Language(TreeSitterJava.language())
        Parser(language).use { parser ->
            parser.parse("void main() {}", InputEncoding.UTF_8).orElseThrow().use { tree ->
                val rootNode: Node = tree.getRootNode()
                assert(rootNode.getType().equals("program"))
                assert(rootNode.getStartPoint().column() === 0)
                assert(rootNode.getEndPoint().column() === 14)
            }
        }
    }
}