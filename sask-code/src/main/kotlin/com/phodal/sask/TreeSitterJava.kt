package com.phodal.sask

import java.lang.invoke.MethodHandle
import java.util.function.Supplier


class TreeSitterJava {
    private val arena: java.lang.foreign.Arena = java.lang.foreign.Arena.ofAuto()
    private val symbols: java.lang.foreign.SymbolLookup = findLibrary()

    private fun findLibrary(): java.lang.foreign.SymbolLookup {
        try {
            val library = System.mapLibraryName("tree-sitter-java")
            return java.lang.foreign.SymbolLookup.libraryLookup(library, arena)
        } catch (e: IllegalArgumentException) {
            return java.lang.foreign.SymbolLookup.loaderLookup()
        }
    }

    @Throws(UnsatisfiedLinkError::class)
    private fun call(name: String?): java.lang.foreign.MemorySegment? {
        val address: java.lang.foreign.MemorySegment? =
            symbols.find(name).orElseThrow<UnsatisfiedLinkError?>(Supplier { unresolved(name) })
        try {
            val function: MethodHandle = LINKER.downcallHandle(address, FUNC_DESC)
            return function.invokeExact() as java.lang.foreign.MemorySegment?
        } catch (e: Throwable) {
            throw RuntimeException("Call to %s failed".formatted(name), e)
        }
    }

    companion object {
        private val VOID_PTR: java.lang.foreign.ValueLayout = java.lang.foreign.ValueLayout.ADDRESS.withTargetLayout(
            java.lang.foreign.MemoryLayout.sequenceLayout(
                Long.Companion.MAX_VALUE, java.lang.foreign.ValueLayout.JAVA_BYTE
            )
        )
        private val FUNC_DESC: java.lang.foreign.FunctionDescriptor = java.lang.foreign.FunctionDescriptor.of(VOID_PTR)
        private val LINKER: java.lang.foreign.Linker = java.lang.foreign.Linker.nativeLinker()
        private val INSTANCE = TreeSitterJava()

        /**
         * {@snippet lang=c :
         * * const TSLanguage *tree_sitter_java()
         * * }
         */
        fun language(): java.lang.foreign.MemorySegment? {
            return INSTANCE.call("tree_sitter_java")
        }

        private fun unresolved(name: String?): UnsatisfiedLinkError {
            return UnsatisfiedLinkError("Unresolved symbol: %s".formatted(name))
        }
    }
}