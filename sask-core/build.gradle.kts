dependencies {
    // Spring AI dependencies
    implementation("org.springframework.ai:spring-ai-starter-model-openai:1.0.1")

    // Spring Boot starters
    implementation("org.springframework.boot:spring-boot-starter:3.5.5")
    implementation("org.springframework.boot:spring-boot-starter-web:3.5.5")

    // Template engine for prompt management
    implementation("com.github.jknack:handlebars:4.3.1")

    // JSON processing
    implementation("com.fasterxml.jackson.core:jackson-databind:2.19.2")

    // Jakarta annotations
    implementation("jakarta.annotation:jakarta.annotation-api:3.0.0")

    // https://mvnrepository.com/artifact/com.microsoft.semantic-kernel/semantickernel-agents-core
    implementation("com.microsoft.semantic-kernel:semantickernel-agents-core:1.4.4-RC1")
    // https://mvnrepository.com/artifact/com.microsoft.semantic-kernel/semantickernel-api
    implementation("com.microsoft.semantic-kernel:semantickernel-api:1.4.4-RC1")

    // Lombok for reducing boilerplate
    compileOnly("org.projectlombok:lombok:1.18.28")
    annotationProcessor("org.projectlombok:lombok:1.18.28")
}
