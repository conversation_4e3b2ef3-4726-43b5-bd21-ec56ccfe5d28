package com.phodal.sask.core.chat

import org.springframework.ai.chat.client.ChatClient
import org.springframework.ai.chat.messages.Message
import org.springframework.ai.chat.model.ChatResponse
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux

/**
 * Chat service that wraps Spring AI ChatClient with Semantic Kernel-like interface
 */
@Service
class ChatService(
    private val chatClient: ChatClient
) {
    
    /**
     * Send a simple text message and get response
     */
    fun chat(message: String): String {
        return chatClient.prompt()
            .user(message)
            .call()
            .content() ?: ""
    }

    /**
     * Send a message with system prompt
     */
    fun chat(systemPrompt: String, userMessage: String): String {
        return chatClient.prompt()
            .system(systemPrompt)
            .user(userMessage)
            .call()
            .content() ?: ""
    }

    /**
     * Send messages and get full ChatResponse
     */
    fun chatWithResponse(messages: List<Message>): ChatResponse? {
        return chatClient.prompt()
            .messages(messages)
            .call()
            .chatResponse()
    }
    
    /**
     * Stream chat response
     */
    fun streamChat(message: String): Flux<String> {
        return chatClient.prompt()
            .user(message)
            .stream()
            .content()
    }
    
    /**
     * Chat with function calling support
     */
    fun chatWithFunctions(message: String, functions: List<String>): String {
        return chatClient.prompt()
            .user(message)
            .call()
            .content() ?: ""
    }
}
