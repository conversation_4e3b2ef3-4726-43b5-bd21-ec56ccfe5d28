package com.phodal.sask.core.kernel

import org.springframework.ai.chat.client.ChatClient
import org.springframework.stereotype.Component

/**
 * SASK Kernel - Core execution engine that adapts Semantic Kernel concepts to Spring AI
 */
@Component
class SaskKernel(
    private val chatClient: ChatClient
) {
    
    private val plugins = mutableMapOf<String, Any>()
    private val functions = mutableMapOf<String, kotlin.Function<*>>()

    /**
     * Register a plugin with the kernel
     */
    fun registerPlugin(name: String, plugin: Any) {
        plugins[name] = plugin
    }

    /**
     * Register a function with the kernel
     */
    fun registerFunction(name: String, function: kotlin.Function<*>) {
        functions[name] = function
    }

    /**
     * Get registered plugin by name
     */
    fun getPlugin(name: String): Any? = plugins[name]

    /**
     * Get registered function by name
     */
    fun getFunction(name: String): kotlin.Function<*>? = functions[name]
    
    /**
     * Get the underlying Spring AI ChatClient
     */
    fun getChatClient(): ChatClient = chatClient
}
