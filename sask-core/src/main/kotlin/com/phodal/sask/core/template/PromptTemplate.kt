package com.phodal.sask.core.template

import com.github.jknack.handlebars.Handlebars
import com.github.jknack.handlebars.Template
import org.springframework.stereotype.Component

/**
 * Prompt template engine using Handlebars for dynamic prompt generation
 */
@Component
class PromptTemplate {
    
    private val handlebars = Handlebars()
    
    /**
     * Compile a template from string
     */
    fun compile(templateString: String): Template {
        return handlebars.compileInline(templateString)
    }
    
    /**
     * Render template with context data
     */
    fun render(templateString: String, context: Map<String, Any>): String {
        val template = compile(templateString)
        return template.apply(context)
    }
    
    /**
     * Render template with context data
     */
    fun render(template: Template, context: Map<String, Any>): String {
        return template.apply(context)
    }
}
