dependencies {
    // Core module dependency
    implementation(project(":sask-core"))

    // Tablesaw for CSV parsing and data manipulation
    implementation("tech.tablesaw:tablesaw-core:0.44.4")
    implementation("tech.tablesaw:tablesaw-json:0.44.4")

    // Spring Boot starters
    implementation("org.springframework.boot:spring-boot-starter:3.5.5")
    implementation("org.springframework.boot:spring-boot-starter-webflux:3.5.5")

    // JSON processing
    implementation("com.fasterxml.jackson.core:jackson-databind:2.19.2")

    // Jakarta annotations
    implementation("jakarta.annotation:jakarta.annotation-api:3.0.0")
}
