package com.phodal.sask.data.loader

import tech.tablesaw.api.Table
import tech.tablesaw.io.csv.CsvReadOptions
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClient
import reactor.core.publisher.Mono
import java.io.File
import java.io.InputStream
import java.net.URL

/**
 * Data loader for loading data from various sources
 */
@Component
class DataLoader(
    private val webClient: WebClient = WebClient.create()
) {
    
    /**
     * Load CSV data from file path
     */
    fun loadCsvFromFile(filePath: String): Table {
        return Table.read().csv(filePath)
    }
    
    /**
     * Load CSV data from InputStream
     */
    fun loadCsvFromStream(inputStream: InputStream, tableName: String = "data"): Table {
        return Table.read().csv(inputStream, tableName)
    }
    
    /**
     * Load CSV data from URL
     */
    fun loadCsvFromUrl(url: String): Table {
        return Table.read().csv(URL(url))
    }
    
    /**
     * Load JSON data from URL asynchronously
     */
    fun loadJsonFromUrl(url: String): Mono<String> {
        return webClient.get()
            .uri(url)
            .retrieve()
            .bodyToMono(String::class.java)
    }
    
    /**
     * Load data from HTTP API
     */
    fun loadFromApi(url: String, headers: Map<String, String> = emptyMap()): Mono<String> {
        var request = webClient.get().uri(url)
        
        headers.forEach { (key, value) ->
            request = request.header(key, value)
        }
        
        return request.retrieve().bodyToMono(String::class.java)
    }
}

/**
 * Data source configuration
 */
data class DataSource(
    val type: DataSourceType,
    val location: String,
    val options: Map<String, Any> = emptyMap()
)

/**
 * Supported data source types
 */
enum class DataSourceType {
    FILE,
    URL,
    API,
    DATABASE,
    STREAM
}
