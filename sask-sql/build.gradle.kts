dependencies {
    // Core module dependency
    implementation(project(":sask-core"))

    // SQL parser for SQL analysis and validation
    implementation("com.github.jsqlparser:jsqlparser:5.0")

    // Spring Boot starters
    implementation("org.springframework.boot:spring-boot-starter:3.5.5")
    implementation("org.springframework.boot:spring-boot-starter-jdbc:3.5.5")

    // JSON processing
    implementation("com.fasterxml.jackson.core:jackson-databind:2.19.2")

    // Jakarta annotations
    implementation("jakarta.annotation:jakarta.annotation-api:3.0.0")
}
