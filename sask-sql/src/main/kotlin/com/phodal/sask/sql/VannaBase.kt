package com.phodal.sask.sql

import java.util.regex.Pattern
import kotlin.collections.List
import kotlin.collections.Map

/**
 * Abstract base class for Vanna AI functionality.
 * This is a Kotlin translation of the Python VannaBase class.
 */
abstract class VannaBase(private val config: Map<String, Any>? = null) {

    var runSqlIsSet: Boolean = false
        protected set

    var staticDocumentation: String = ""
        protected set

    protected var runSql: ((String) -> DataFrame)? = null

    /**
     * Log a message to the console
     */
    fun log(message: String) {
        println(message)
    }

    /**
     * Generate SQL query from a natural language question
     */
    fun generateSql(question: String, vararg kwargs: Pair<String, Any>): String {
        val questionSqlList = getSimilarQuestionSql(question, *kwargs)
        val ddlList = getRelatedDdl(question, *kwargs)
        val docList = getRelatedDocumentation(question, *kwargs)
        val prompt = getSqlPrompt(
            question = question,
            questionSqlList = questionSqlList,
            ddlList = ddlList,
            docList = docList,
            *kwargs
        )
        log(prompt.toString())
        val llmResponse = submitPrompt(prompt, *kwargs)
        log(llmResponse)
        return extractSql(llmResponse)
    }

    /**
     * Extract SQL from LLM response
     */
    fun extractSql(llmResponse: String): String {
        // Try to extract SQL from markdown code block with sql tag
        val sqlPattern = Pattern.compile("```sql\\n(.*)```", Pattern.DOTALL)
        val sqlMatcher = sqlPattern.matcher(llmResponse)
        if (sqlMatcher.find()) {
            val extractedSql = sqlMatcher.group(1)
            log("Output from LLM: $llmResponse \nExtracted SQL: $extractedSql")
            return extractedSql
        }

        // Try to extract from any markdown code block
        val codePattern = Pattern.compile("```(.*)```", Pattern.DOTALL)
        val codeMatcher = codePattern.matcher(llmResponse)
        if (codeMatcher.find()) {
            val extractedSql = codeMatcher.group(1)
            log("Output from LLM: $llmResponse \nExtracted SQL: $extractedSql")
            return extractedSql
        }

        return llmResponse
    }

    /**
     * Check if SQL is valid (simple check for SELECT statement)
     */
    fun isSqlValid(sql: String): Boolean {
        return sql.uppercase().contains("SELECT")
    }

    /**
     * Generate followup questions based on the original question, SQL, and results
     */
    fun generateFollowupQuestions(
        question: String,
        sql: String,
        df: DataFrame,
        vararg kwargs: Pair<String, Any>
    ): List<String> {
        val messageLog = listOf(
            systemMessage(
                "You are a helpful data assistant. The user asked the question: '$question'\n\n" +
                "The SQL query for this question was: $sql\n\n" +
                "The following is a DataFrame with the results of the query: \n${df.toMarkdown()}\n\n"
            ),
            userMessage(
                "Generate a list of followup questions that the user might ask about this data. " +
                "Respond with a list of questions, one per line. Do not answer with any explanations -- just the questions. " +
                "Remember that there should be an unambiguous SQL query that can be generated from the question. " +
                "Prefer questions that are answerable outside of the context of this conversation. " +
                "Prefer questions that are slight modifications of the SQL query that was generated that allow digging deeper into the data. " +
                "Each question will be turned into a button that the user can click to generate a new SQL query so don't use 'example' type questions. " +
                "Each question must have a one-to-one correspondence with an instantiated SQL query."
            )
        )

        val llmResponse = submitPrompt(messageLog, *kwargs)
        val numbersRemoved = llmResponse.replace(Regex("^\\d+\\.\\s*", RegexOption.MULTILINE), "")
        return numbersRemoved.split("\n").filter { it.isNotBlank() }
    }

    /**
     * Generate a list of questions that can be asked
     */
    fun generateQuestions(vararg kwargs: Pair<String, Any>): List<String> {
        val questionSql = getSimilarQuestionSql("", *kwargs)
        return questionSql.map { it["question"] as? String ?: "" }.filter { it.isNotBlank() }
    }

    /**
     * Generate a summary of the results
     */
    fun generateSummary(question: String, df: DataFrame, vararg kwargs: Pair<String, Any>): String {
        val messageLog = listOf(
            systemMessage(
                "You are a helpful data assistant. The user asked the question: '$question'\n\n" +
                "The following is a DataFrame with the results of the query: \n${df.toMarkdown()}\n\n"
            ),
            userMessage(
                "Briefly summarize the data based on the question that was asked. " +
                "Do not respond with any additional explanation beyond the summary."
            )
        )

        return submitPrompt(messageLog, *kwargs)
    }

    /**
     * Convert string to approximate token count
     */
    fun strToApproxTokenCount(string: String): Int {
        return string.length / 4
    }

    /**
     * Add DDL statements to prompt
     */
    fun addDdlToPrompt(
        initialPrompt: String,
        ddlList: List<String>,
        maxTokens: Int = 14000
    ): String {
        var prompt = initialPrompt
        if (ddlList.isNotEmpty()) {
            prompt += "\nYou may use the following DDL statements as a reference for what tables might be available. " +
                    "Use responses to past questions also to guide you:\n\n"

            for (ddl in ddlList) {
                if (strToApproxTokenCount(prompt) + strToApproxTokenCount(ddl) < maxTokens) {
                    prompt += "$ddl\n\n"
                }
            }
        }
        return prompt
    }

    /**
     * Add documentation to prompt
     */
    fun addDocumentationToPrompt(
        initialPrompt: String,
        documentationList: List<String>,
        maxTokens: Int = 14000
    ): String {
        var prompt = initialPrompt
        if (documentationList.isNotEmpty()) {
            prompt += "\nYou may use the following documentation as a reference for what tables might be available. " +
                    "Use responses to past questions also to guide you:\n\n"

            for (documentation in documentationList) {
                if (strToApproxTokenCount(prompt) + strToApproxTokenCount(documentation) < maxTokens) {
                    prompt += "$documentation\n\n"
                }
            }
        }
        return prompt
    }

    /**
     * Add SQL examples to prompt
     */
    fun addSqlToPrompt(
        initialPrompt: String,
        sqlList: List<Map<String, String>>,
        maxTokens: Int = 14000
    ): String {
        var prompt = initialPrompt
        if (sqlList.isNotEmpty()) {
            prompt += "\nYou may use the following SQL statements as a reference for what tables might be available. " +
                    "Use responses to past questions also to guide you:\n\n"

            for (question in sqlList) {
                val sql = question["sql"] ?: ""
                if (strToApproxTokenCount(prompt) + strToApproxTokenCount(sql) < maxTokens) {
                    prompt += "${question["question"]}\n$sql\n\n"
                }
            }
        }
        return prompt
    }

    /**
     * Generate SQL prompt for LLM
     */
    fun getSqlPrompt(
        question: String,
        questionSqlList: List<Map<String, String>>,
        ddlList: List<String>,
        docList: List<String>,
        vararg kwargs: Pair<String, Any>
    ): List<Any> {
        var initialPrompt = "The user provides a question and you provide SQL. You will only respond with SQL code and not with any explanations.\n\n" +
                "Respond with only SQL code. Do not answer with any explanations -- just the code.\n"

        initialPrompt = addDdlToPrompt(initialPrompt, ddlList, 14000)

        val docListMutable = docList.toMutableList()
        if (staticDocumentation.isNotEmpty()) {
            docListMutable.add(staticDocumentation)
        }

        initialPrompt = addDocumentationToPrompt(initialPrompt, docListMutable, 14000)

        val messageLog = mutableListOf<Any>(systemMessage(initialPrompt))

        for (example in questionSqlList) {
            if (example.containsKey("question") && example.containsKey("sql")) {
                messageLog.add(userMessage(example["question"] ?: ""))
                messageLog.add(assistantMessage(example["sql"] ?: ""))
            }
        }

        messageLog.add(userMessage(question))

        return messageLog
    }

    /**
     * Generate a question from SQL
     */
    fun generateQuestion(sql: String, vararg kwargs: Pair<String, Any>): String {
        return submitPrompt(
            listOf(
                systemMessage(
                    "The user will give you SQL and you will try to guess what the business question this query is answering. " +
                    "Return just the question without any additional explanation. Do not reference the table name in the question."
                ),
                userMessage(sql)
            ),
            *kwargs
        )
    }

    /**
     * Run SQL query and return results as DataFrame
     */
    open fun runSql(sql: String, vararg kwargs: Pair<String, Any>): DataFrame {
        throw Exception(
            "You need to connect to a database first by running connectToDatabase() or similar function, " +
            "or manually set runSql"
        )
    }

    /**
     * Generate embeddings for the given data
     */
    abstract fun generateEmbedding(data: String, vararg kwargs: Pair<String, Any>): List<Float>

    /**
     * Get similar questions and their corresponding SQL statements
     */
    abstract fun getSimilarQuestionSql(question: String, vararg kwargs: Pair<String, Any>): List<Map<String, String>>

    /**
     * Get related DDL statements for a question
     */
    abstract fun getRelatedDdl(question: String, vararg kwargs: Pair<String, Any>): List<String>

    /**
     * Get related documentation for a question
     */
    abstract fun getRelatedDocumentation(question: String, vararg kwargs: Pair<String, Any>): List<String>

    /**
     * Add a question and SQL pair to training data
     */
    abstract fun addQuestionSql(question: String, sql: String, vararg kwargs: Pair<String, Any>): String

    /**
     * Add DDL statement to training data
     */
    abstract fun addDdl(ddl: String, vararg kwargs: Pair<String, Any>): String

    /**
     * Add documentation to training data
     */
    abstract fun addDocumentation(documentation: String, vararg kwargs: Pair<String, Any>): String

    /**
     * Get all training data
     */
    abstract fun getTrainingData(vararg kwargs: Pair<String, Any>): DataFrame

    /**
     * Remove training data by ID
     */
    abstract fun removeTrainingData(id: String, vararg kwargs: Pair<String, Any>): Boolean

    /**
     * Create system message
     */
    abstract fun systemMessage(message: String): Any

    /**
     * Create user message
     */
    abstract fun userMessage(message: String): Any

    /**
     * Create assistant message
     */
    abstract fun assistantMessage(message: String): Any

    /**
     * Submit prompt to LLM and get response
     */
    abstract fun submitPrompt(prompt: Any, vararg kwargs: Pair<String, Any>): String
}

/**
 * Simple DataFrame interface for representing tabular data
 */
interface DataFrame {
    fun toMarkdown(): String
}
